import React, { useEffect, useMemo, useState } from "react";

// -------- types --------
type CharacterKey = "Squeaky" | "Bouncy" | "Tilly";
type Difficulty = "Normal" | "Hard";
type LogItem = { ts: number; text: string };
type EventCard = { key: string; label: string; bias: number; description: string };

// -------- helpers --------
const clamp = (n: number, lo: number, hi: number) => Math.max(lo, Math.min(hi, n));
const round2 = (n: number) => Math.round(n * 100) / 100;
const money = (n: number) => `$${n.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;

// SFX (no assets): tiny beeps via WebAudio
const createSfx = (soundEnabled: boolean, volume: number) => ({
  click: (freq = 520) => {
    if (!soundEnabled) return;
    try {
      const ctx = new (window.AudioContext || (window as any).webkitAudioContext)();
      const o = ctx.createOscillator(); const g = ctx.createGain();
      o.connect(g); g.connect(ctx.destination);
      o.type = "sine"; o.frequency.value = freq; g.gain.value = volume;
      o.start(); setTimeout(() => { o.stop(); ctx.close(); }, 120);
    } catch {}
  },
  good: function() { this.click(880); },
  bad: function() { this.click(220); },
});

// Dice maps (Normal mirrors your PDF; Hard is spicier)
const DICE_MAPS: Record<Difficulty, Record<number, number>> = {
  Normal: { 1: -0.10, 2: 0, 3: 0, 4: +0.10, 5: +0.10, 6: +0.20 },
  Hard:   { 1: -0.15, 2: -0.05, 3: 0, 4: +0.05, 5: +0.10, 6: +0.15 },
};

// Daily event cards (bias is additive to today's pct move)
const EVENT_DECK: EventCard[] = [
  { key: "rumor",   label: "Rumor (-5%)",     bias: -0.05, description: "Spooky chatter. Sellers nibble." },
  { key: "harvest", label: "Good Harvest (+5%)", bias: +0.05, description: "Plenty of acorns. Optimism rises." },
  { key: "storm",   label: "Storm (-10%)",    bias: -0.10, description: "Supply chains muddy. Risk-off." },
  { key: "fair",    label: "Market Fair (+10%)", bias: +0.10, description: "Festival boom! Buyers abound." },
];

const STORAGE_KEY = "acorn-market-v1";

export default function App() {
  // ------- state -------
  const [day, setDay]                 = useState(1);
  const [maxDays, setMaxDays]         = useState(20);
  const [price, setPrice]             = useState(1.00);
  const [history, setHistory]         = useState<number[]>([1.00]);
  const [cash, setCash]               = useState(100);
  const [acorns, setAcorns]           = useState(0);
  const [goal, setGoal]               = useState(250);
  const [qty, setQty]                 = useState(1);
  const [selectedChar, setSelectedChar] = useState<CharacterKey | null>(null);
  const [lastRoll, setLastRoll]       = useState<number | null>(null);
  const [lastChange, setLastChange]   = useState(0);
  const [logs, setLogs]               = useState<LogItem[]>([]);
  const [difficulty, setDifficulty]   = useState<Difficulty>("Normal");
  const [eventToday, setEventToday]   = useState<EventCard | null>(null);

  // perks
  const [rerollAvailable, setRerollAvailable] = useState(false);  // Bouncy
  const [rerollUsed, setRerollUsed]           = useState(false);  // Bouncy
  const [squeakyDiscountReady, setSqueakyDiscountReady] = useState(false); // Squeaky (after down move)

  // animations
  const [isRolling, setIsRolling] = useState(false);
  const [hudFlash, setHudFlash] = useState<"" | "flash-green" | "flash-red">("");

  // save/load
  const [showSaveLoad, setShowSaveLoad] = useState(false);

  // event tracking
  const [eventHistory, setEventHistory] = useState<EventCard[]>([]);
  const [showEventDeck, setShowEventDeck] = useState(false);

  // settings
  const [showSettings, setShowSettings] = useState(false);
  const [reduceMotion, setReduceMotion] = useState(false);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [soundVolume, setSoundVolume] = useState(0.05);

  // ------- computed -------
  const netWorth = useMemo(() => round2(cash + acorns * price), [cash, acorns, price]);
  const canBuyQty = useMemo(() => Math.floor(cash / effectivePriceToBuy()), [cash, price, selectedChar, squeakyDiscountReady]);
  const sfx = useMemo(() => createSfx(soundEnabled, soundVolume), [soundEnabled, soundVolume]);

  const won  = netWorth >= goal;
  const lost = day > maxDays && !won;

  // ------- persistence -------
  useEffect(() => {
    // load
    const raw = localStorage.getItem(STORAGE_KEY);
    if (raw) {
      try {
        const s = JSON.parse(raw);
        setDay(s.day ?? 1); setMaxDays(s.maxDays ?? 20);
        setPrice(s.price ?? 1); setHistory(s.history ?? [1]);
        setCash(s.cash ?? 100); setAcorns(s.acorns ?? 0);
        setGoal(s.goal ?? 250); setQty(s.qty ?? 1);
        setSelectedChar(s.selectedChar ?? null);
        setLastRoll(s.lastRoll ?? null); setLastChange(s.lastChange ?? 0);
        setLogs(s.logs ?? []); setDifficulty(s.difficulty ?? "Normal");
        setEventToday(s.eventToday ?? null);
        setRerollAvailable(s.rerollAvailable ?? false);
        setRerollUsed(s.rerollUsed ?? false);
        setSqueakyDiscountReady(s.squeakyDiscountReady ?? false);
      } catch {}
    } else {
      // seed first day event
      drawEventForToday();
    }
  }, []);

  useEffect(() => {
    // save
    const s = {
      day, maxDays, price, history, cash, acorns, goal, qty,
      selectedChar, lastRoll, lastChange, logs, difficulty,
      eventToday, rerollAvailable, rerollUsed, squeakyDiscountReady
    };
    localStorage.setItem(STORAGE_KEY, JSON.stringify(s));
  }, [day, maxDays, price, history, cash, acorns, goal, qty, selectedChar, lastRoll, lastChange, logs, difficulty, eventToday, rerollAvailable, rerollUsed, squeakyDiscountReady]);

  // ------- utils -------
  function log(msg: string) {
    setLogs(l => [{ ts: Date.now(), text: msg }, ...l].slice(0, 80));
  }

  function resetGame() {
    setDay(1); setMaxDays(20); setPrice(1); setHistory([1]);
    setCash(100); setAcorns(0); setGoal(250); setQty(1);
    setSelectedChar(null); setLastRoll(null); setLastChange(0);
    setLogs([]); setDifficulty("Normal"); setEventToday(null);
    setRerollAvailable(false); setRerollUsed(false); setSqueakyDiscountReady(false);
    setEventHistory([]);
    drawEventForToday();
  }

  function startAs(c: CharacterKey) {
    setSelectedChar(c);
    setRerollAvailable(c === "Bouncy");
    setRerollUsed(false);
    log(`Started as ${c}.`);
  }

  function effectivePriceToBuy() {
    const discount = selectedChar === "Squeaky" && squeakyDiscountReady ? 0.05 : 0;
    return round2(price * (1 - discount));
  }

  function drawEventForToday() {
    // ~40% chance to draw an event
    const r = Math.random();
    if (r < 0.4) {
      const card = EVENT_DECK[Math.floor(Math.random() * EVENT_DECK.length)];
      setEventToday(card);
      setEventHistory(h => [card, ...h].slice(0, 10)); // Keep last 10 events
      log(`Event: ${card.label} — ${card.description}`);
    } else {
      setEventToday(null);
    }
  }

  function createConfetti() {
    if (reduceMotion) return; // Skip confetti if motion is reduced

    const confettiEmojis = ['🎉', '✨', '🎊', '⭐', '💫', '🌟'];
    for (let i = 0; i < 15; i++) {
      setTimeout(() => {
        const confetti = document.createElement('div');
        confetti.className = 'confetti';
        confetti.textContent = confettiEmojis[Math.floor(Math.random() * confettiEmojis.length)];
        confetti.style.left = Math.random() * window.innerWidth + 'px';
        confetti.style.top = Math.random() * 100 + 'px';
        document.body.appendChild(confetti);

        setTimeout(() => {
          if (confetti.parentNode) {
            confetti.parentNode.removeChild(confetti);
          }
        }, 2000);
      }, i * 100);
    }
  }

  function saveToSlot(slot: 'A' | 'B' | 'C') {
    const gameState = {
      day, maxDays, price, history, cash, acorns, goal, qty,
      selectedChar, lastRoll, lastChange, logs, difficulty,
      eventToday, rerollAvailable, rerollUsed, squeakyDiscountReady,
      savedAt: new Date().toISOString()
    };
    localStorage.setItem(`acorn-market-slot-${slot}`, JSON.stringify(gameState));
    log(`Game saved to slot ${slot}.`);
    sfx.good();
  }

  function loadFromSlot(slot: 'A' | 'B' | 'C') {
    const raw = localStorage.getItem(`acorn-market-slot-${slot}`);
    if (!raw) {
      log(`No save found in slot ${slot}.`);
      sfx.bad();
      return;
    }

    try {
      const s = JSON.parse(raw);
      setDay(s.day ?? 1); setMaxDays(s.maxDays ?? 20);
      setPrice(s.price ?? 1); setHistory(s.history ?? [1]);
      setCash(s.cash ?? 100); setAcorns(s.acorns ?? 0);
      setGoal(s.goal ?? 250); setQty(s.qty ?? 1);
      setSelectedChar(s.selectedChar ?? null);
      setLastRoll(s.lastRoll ?? null); setLastChange(s.lastChange ?? 0);
      setLogs(s.logs ?? []); setDifficulty(s.difficulty ?? "Normal");
      setEventToday(s.eventToday ?? null);
      setRerollAvailable(s.rerollAvailable ?? false);
      setRerollUsed(s.rerollUsed ?? false);
      setSqueakyDiscountReady(s.squeakyDiscountReady ?? false);
      log(`Game loaded from slot ${slot}.`);
      sfx.good();
    } catch {
      log(`Failed to load from slot ${slot}.`);
      sfx.bad();
    }
  }

  function getSaveSlotInfo(slot: 'A' | 'B' | 'C') {
    const raw = localStorage.getItem(`acorn-market-slot-${slot}`);
    if (!raw) return null;

    try {
      const s = JSON.parse(raw);
      return {
        day: s.day ?? 1,
        character: s.selectedChar ?? "None",
        netWorth: round2((s.cash ?? 0) + (s.acorns ?? 0) * (s.price ?? 1)),
        savedAt: s.savedAt ? new Date(s.savedAt).toLocaleDateString() : "Unknown"
      };
    } catch {
      return null;
    }
  }

  function shareRun() {
    const gameState = {
      day, maxDays, price, history, cash, acorns, goal,
      selectedChar, lastRoll, lastChange, difficulty,
      eventToday, netWorth: round2(cash + acorns * price)
    };

    const encoded = btoa(JSON.stringify(gameState));
    const shareUrl = `${window.location.origin}${window.location.pathname}?share=${encoded}`;

    if (navigator.clipboard) {
      navigator.clipboard.writeText(shareUrl).then(() => {
        log("Share URL copied to clipboard!");
        sfx.good();
      }).catch(() => {
        log("Failed to copy URL. Check console.");
        console.log("Share URL:", shareUrl);
        sfx.bad();
      });
    } else {
      log("Share URL logged to console.");
      console.log("Share URL:", shareUrl);
      sfx.click();
    }
  }

  // Load shared game state from URL on mount
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const shareData = urlParams.get('share');
    if (shareData) {
      try {
        const gameState = JSON.parse(atob(shareData));
        setDay(gameState.day ?? 1);
        setMaxDays(gameState.maxDays ?? 20);
        setPrice(gameState.price ?? 1);
        setHistory(gameState.history ?? [1]);
        setCash(gameState.cash ?? 100);
        setAcorns(gameState.acorns ?? 0);
        setGoal(gameState.goal ?? 250);
        setSelectedChar(gameState.selectedChar ?? null);
        setLastRoll(gameState.lastRoll ?? null);
        setLastChange(gameState.lastChange ?? 0);
        setDifficulty(gameState.difficulty ?? "Normal");
        setEventToday(gameState.eventToday ?? null);
        log("Loaded shared game state!");

        // Clear the URL parameter
        window.history.replaceState({}, document.title, window.location.pathname);
      } catch {
        log("Failed to load shared game state.");
      }
    }
  }, []);

  function applyRoll(roll: number) {
    const basePct = DICE_MAPS[difficulty][roll as 1|2|3|4|5|6] ?? 0;
    const bias = eventToday?.bias ?? 0;
    // Additive bias; clamp total change to ±50% to avoid blowups
    const pct = clamp(basePct + bias, -0.5, 0.5);
    const newPrice = clamp(round2(price * (1 + pct)), 0.01, 1_000_000);

    setPrice(newPrice);
    setHistory(h => [...h, newPrice].slice(-240));
    setLastRoll(roll);
    setLastChange(pct);

    // Squeaky: if down move, flag discount for next buy (today only)
    if (selectedChar === "Squeaky") setSqueakyDiscountReady(pct < 0);

    // Flash animation based on price movement (if motion not reduced)
    if (!reduceMotion) {
      if (pct > 0) {
        setHudFlash("flash-green");
        setTimeout(() => setHudFlash(""), 600);
      } else if (pct < 0) {
        setHudFlash("flash-red");
        setTimeout(() => setHudFlash(""), 600);
      }
    }

    // Confetti on a 6!
    if (roll === 6) {
      createConfetti();
    }

    const arrow = pct === 0 ? "unchanged" : (pct > 0 ? `up ${pct * 100}%` : `down ${Math.abs(pct) * 100}%`);
    log(`Day ${day}: rolled ${roll}. Price ${arrow} to ${newPrice}.`);
  }

  function rollDice() {
    if (won || lost || isRolling) return;

    setIsRolling(true);
    sfx.click();

    // Animate dice spin for 500ms then show result (or instant if motion reduced)
    const delay = reduceMotion ? 0 : 500;
    setTimeout(() => {
      const r = Math.floor(Math.random() * 6) + 1;
      applyRoll(r);
      setIsRolling(false);
    }, delay);
  }

  function reroll() {
    if (won || lost || isRolling) return;
    if (selectedChar !== "Bouncy" || !rerollAvailable || rerollUsed) return;

    setIsRolling(true);
    sfx.click(640);

    const delay = reduceMotion ? 0 : 500;
    setTimeout(() => {
      const r = Math.floor(Math.random() * 6) + 1;
      applyRoll(r);
      setRerollUsed(true);
      log("Bouncy perk: used reroll.");
      setIsRolling(false);
    }, delay);
  }

  function buy() {
    const q = Math.max(0, Math.floor(qty));
    if (!q) return;

    const effective = effectivePriceToBuy();
    const cost = round2(q * effective);
    if (cost > cash) { log("Not enough cash."); sfx.bad(); return; }

    setCash(c => round2(c - cost));
    setAcorns(a => a + q);
    log(`Bought ${q} @ ${effective}${(selectedChar === "Squeaky" && squeakyDiscountReady) ? " (Squeaky -5% discount)" : ""}. Cost ${money(cost)}.`);
    // Consume Squeaky discount after 1 buy
    if (selectedChar === "Squeaky" && squeakyDiscountReady) setSqueakyDiscountReady(false);
    sfx.click();
  }

  function sell() {
    const q = Math.max(0, Math.floor(qty));
    if (!q) return;
    if (q > acorns) { log("Not enough acorns."); sfx.bad(); return; }

    const proceeds = round2(q * price);
    setCash(c => round2(c + proceeds));
    setAcorns(a => a - q);
    log(`Sold ${q} @ ${price}. Proceeds ${money(proceeds)}.`);
    sfx.click(700);
  }

  function endDay() {
    if (won || lost) return;

    // Tilly: 1% dividend on holdings
    if (selectedChar === "Tilly" && acorns > 0) {
      const div = round2(acorns * price * 0.01);
      if (div > 0) {
        setCash(c => round2(c + div));
        log(`Tilly dividend: ${money(div)} added to cash.`);
        sfx.good();
      }
    }

    // Reset daily perks
    if (selectedChar === "Bouncy") { setRerollAvailable(true); setRerollUsed(false); }
    setSqueakyDiscountReady(false);

    // Next day
    setDay(d => d + 1);
    drawEventForToday();
  }

  // ------- UI -------
  if (!selectedChar) {
    return (
      <div className="shell">
        <header className="hero">
          <h1>Acorn Market</h1>
          <p>Buy low, sell high, and use your character's perk to hit the goal before you run out of days.</p>
        </header>

        <section className="card grid3">
          {(["Squeaky","Bouncy","Tilly"] as CharacterKey[]).map((c) => (
            <button key={c} className="char" onClick={() => startAs(c)}>
              <div className="avatar">{c === "Squeaky" ? "🐿️" : c === "Bouncy" ? "🐰" : "🐢"}</div>
              <div className="name">{c}</div>
              <div className="perk">
                {c === "Squeaky" && "5% buy discount after a down move (today only)."}
                {c === "Bouncy" && "One reroll per day."}
                {c === "Tilly"   && "1% dividend on holdings at End Day."}
              </div>
            </button>
          ))}
        </section>

        <section className="row">
          <div className="card">
            <h3>Difficulty</h3>
            <div className="controls">
              <select value={difficulty} onChange={e => setDifficulty(e.target.value as Difficulty)}>
                <option>Normal</option>
                <option>Hard</option>
              </select>
            </div>
            <p className="hint">Normal uses your PDF dice map; Hard adds more downside and less upside.</p>
          </div>
          <div className="card">
            <h3>Goal & Days</h3>
            <div className="controls">
              <label>Goal <input type="number" value={goal} min={50} onChange={e=>setGoal(parseInt(e.target.value||"0",10))}/></label>
              <label>Max Days <input type="number" value={maxDays} min={5} onChange={e=>setMaxDays(parseInt(e.target.value||"0",10))}/></label>
            </div>
          </div>
        </section>

        <div className="footer-actions">
          <button className="ghost" onClick={resetGame}>Reset</button>
        </div>

        <footer className="footnote">Dice rules based on your PDF (1=−10%, 2/3=0%, 4/5=+10%, 6=+20%).</footer>
      </div>
    );
  }

  return (
    <div className="shell">
      <header className={`hud ${hudFlash}`}>
        <div><b>Day</b> {day} / {maxDays}</div>
        <div><b>Price</b> {price.toFixed(2)}</div>
        <div><b>Net Worth</b> {money(netWorth)}</div>
        <div className={`dice ${isRolling && !reduceMotion ? 'spinning' : ''}`}>
          🎲 {isRolling ? "..." : (lastRoll ?? "—")} {!isRolling && lastChange ? `(${lastChange>0?"+":""}${(lastChange*100).toFixed(0)}%)` : ""}
          {lastChange > 0 && " ↗️"} {lastChange < 0 && " ↘️"}
        </div>
      </header>

      <section className="board">
        {showEventDeck && (
          <div className="event-sidebar">
            <div className="card">
              <h3>Event Deck</h3>
              <button className="ghost small" onClick={() => setShowEventDeck(false)}>Hide</button>

              <div className="event-probabilities">
                <h4>Event Chances (40% daily)</h4>
                {EVENT_DECK.map(event => (
                  <div key={event.key} className="event-prob">
                    <span className="event-name">{event.label}</span>
                    <span className="event-chance">10%</span>
                  </div>
                ))}
                <div className="event-prob">
                  <span className="event-name">No Event</span>
                  <span className="event-chance">60%</span>
                </div>
              </div>

              <div className="event-recent">
                <h4>Recent Events</h4>
                {eventHistory.length === 0 ? (
                  <div className="hint">No events yet</div>
                ) : (
                  <ul>
                    {eventHistory.map((event, i) => (
                      <li key={i} className="recent-event">
                        <span>{event.label}</span>
                        <span className="event-bias">{event.bias > 0 ? '+' : ''}{(event.bias * 100).toFixed(0)}%</span>
                      </li>
                    ))}
                  </ul>
                )}
              </div>
            </div>
          </div>
        )}

        <div className="left">
          <div className="card">
            <h3>Market</h3>
            <Sparkline data={history}/>
            {eventToday ? (
              <div className="badge">{eventToday.label}</div>
            ) : (
              <div className="hint">No special event today.</div>
            )}
            <div className="actions">
              <button onClick={rollDice} disabled={won||lost||isRolling}>
                {isRolling ? "Rolling..." : "Roll Dice"}
              </button>
              <button onClick={reroll} disabled={!(selectedChar==="Bouncy" && rerollAvailable && !rerollUsed) || won || lost || isRolling}>
                {isRolling ? "Rolling..." : "Use Reroll (Bouncy)"}
              </button>
              <button onClick={endDay} disabled={won||lost||isRolling}>End Day</button>
              <button className="ghost small" onClick={() => setShowEventDeck(!showEventDeck)}>
                {showEventDeck ? "Hide Events" : "Show Events"}
              </button>
            </div>
          </div>

          <div className="card">
            <h3>Trade</h3>
            <div className="controls">
              <label>Qty
                <input type="number" min={1} step={1} value={qty} onChange={e=>setQty(parseInt(e.target.value||"0",10))}/>
              </label>
              <button className="buy" onClick={buy}>Buy @ {effectivePriceToBuy().toFixed(2)}</button>
              <button className="sell" onClick={sell}>Sell @ {price.toFixed(2)}</button>
              <div className="hint">You can buy up to <b>{canBuyQty}</b> now.</div>
            </div>
            {(selectedChar==="Squeaky" && squeakyDiscountReady) && <div className="note">Squeaky: 5% discount active (consumed on next buy).</div>}
          </div>

          <div className="card log">
            <h3>Activity</h3>
            <ul>{logs.map(l => <li key={l.ts}>• {l.text}</li>)}</ul>
          </div>
        </div>

        <div className="right">
          <div className="card">
            <h3>Player</h3>
            <div className="player">
              <div className="avatar lg">{selectedChar==="Squeaky"?"🐿️":selectedChar==="Bouncy"?"🐰":"🐢"}</div>
              <div className="stats">
                <div><span>Cash</span><b>{money(cash)}</b></div>
                <div><span>Holdings</span><b>{acorns} acorns</b></div>
                <div><span>Goal</span><b>{money(goal)}</b></div>
                <div><span>Days Left</span><b>{Math.max(0, maxDays - day + 1)}</b></div>
              </div>
            </div>
            <div className="controls two">
              <label>Goal <input type="number" value={goal} min={50} onChange={e=>setGoal(parseInt(e.target.value||"0",10))}/></label>
              <label>Max Days <input type="number" value={maxDays} min={5} onChange={e=>setMaxDays(parseInt(e.target.value||"0",10))}/></label>
              <label>Difficulty
                <select value={difficulty} onChange={e=>setDifficulty(e.target.value as Difficulty)}>
                  <option>Normal</option><option>Hard</option>
                </select>
              </label>
            </div>

            {(won || lost) && (
              <div className={`result ${won ? "win" : "lose"}`}>
                <b>{won ? "Victory!" : "Out of time."}</b>
                <div className="hint">{won ? `You hit ${money(goal)} in ${day} days.` : `Try a different strategy or perk.`}</div>
              </div>
            )}

            <div className="controls">
              <button className="ghost" onClick={() => setShowSaveLoad(!showSaveLoad)}>
                {showSaveLoad ? "Hide" : "Save/Load"}
              </button>
              <button className="ghost" onClick={() => setShowSettings(!showSettings)}>
                {showSettings ? "Hide" : "Settings"}
              </button>
              <button className="ghost" onClick={shareRun}>Share Run</button>
              <button className="ghost" onClick={resetGame}>New Game</button>
            </div>

            {showSaveLoad && (
              <div className="save-load">
                <h4>Save/Load Slots</h4>
                {(['A', 'B', 'C'] as const).map(slot => {
                  const info = getSaveSlotInfo(slot);
                  return (
                    <div key={slot} className="slot">
                      <div className="slot-info">
                        <b>Slot {slot}</b>
                        {info ? (
                          <div className="slot-details">
                            Day {info.day} • {info.character} • {money(info.netWorth)} • {info.savedAt}
                          </div>
                        ) : (
                          <div className="slot-empty">Empty</div>
                        )}
                      </div>
                      <div className="slot-actions">
                        <button onClick={() => saveToSlot(slot)}>Save</button>
                        <button onClick={() => loadFromSlot(slot)} disabled={!info}>Load</button>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}

            {showSettings && (
              <div className="settings">
                <h4>Settings</h4>
                <div className="setting">
                  <label>
                    <input
                      type="checkbox"
                      checked={soundEnabled}
                      onChange={e => setSoundEnabled(e.target.checked)}
                    />
                    Sound Effects
                  </label>
                </div>
                {soundEnabled && (
                  <div className="setting">
                    <label>
                      Volume: {Math.round(soundVolume * 100)}%
                      <input
                        type="range"
                        min="0"
                        max="0.2"
                        step="0.01"
                        value={soundVolume}
                        onChange={e => setSoundVolume(parseFloat(e.target.value))}
                      />
                    </label>
                  </div>
                )}
                <div className="setting">
                  <label>
                    <input
                      type="checkbox"
                      checked={reduceMotion}
                      onChange={e => setReduceMotion(e.target.checked)}
                    />
                    Reduce Motion (disables confetti & animations)
                  </label>
                </div>
              </div>
            )}
          </div>

          <div className="card">
            <h3>Rules</h3>
            <ul className="rules">
              <li>Roll 1 → price −10%</li>
              <li>Roll 2 or 3 → price unchanged</li>
              <li>Roll 4 or 5 → price +10%</li>
              <li>Roll 6 → price +20%</li>
            </ul>
            <p className="hint">Buy low, sell high; use your perk.</p>
          </div>
        </div>
      </section>
    </div>
  );
}

// -------- Sparkline component --------
function Sparkline({ data }: { data: number[] }) {
  const W = 640, H = 160, pad = 10;
  if (data.length < 2) return <div className="sparkline placeholder"/>;
  const min = Math.min(...data), max = Math.max(...data);
  const rng = max - min || 1;
  const pts = data.map((v, i) => {
    const x = pad + (i / (data.length - 1)) * (W - 2 * pad);
    const y = pad + (1 - (v - min) / rng) * (H - 2 * pad);
    return `${x},${y}`;
  }).join(" ");
  const up = data[data.length - 1] >= data[0];
  return (
    <svg className="sparkline" viewBox={`0 0 ${W} ${H}`}>
      <polyline fill="none" stroke={up ? "#16a34a" : "#dc2626"} strokeWidth="3" points={pts} strokeLinejoin="round" strokeLinecap="round"/>
    </svg>
  );
}
