:root{
  --bg:#f6f0e8; --card:#ffffff; --ink:#2b2b2b; --muted:#6b7280;
  --green:#16a34a; --red:#dc2626; --amber:#efb14a; --wood:#a3652a;
  --ring:rgba(0,0,0,.06);
}
*{box-sizing:border-box}
html,body,#root{height:100%}
body{margin:0;background:linear-gradient(180deg,#f9f6f1, #f1eadf); color:var(--ink); font: 16px/1.4 Inter, system-ui, -apple-system, Segoe UI, Roboto, Arial;}
h1,h2,h3{margin:.2rem 0  .6rem}
.shell{max-width:1200px;margin:0 auto;padding:20px}
.hero{text-align:center;margin:16px 0 20px}
.hero h1{font-size:40px;margin-bottom:4px}
.card{background:var(--card); border-radius:16px; padding:16px; box-shadow:0 6px 20px var(--ring)}
.grid3{display:grid; grid-template-columns:repeat(3,1fr); gap:12px}
.char{border:1px solid #e5e7eb; border-radius:16px; background:#fff; padding:16px; text-align:left; cursor:pointer; transition:.2s;}
.char:hover{box-shadow:0 8px 22px var(--ring); transform:translateY(-1px)}
.char .avatar{font-size:44px}
.char .name{font-weight:800; margin:6px 0}
.perk,.hint{color:var(--muted); font-size:13px}
.row{display:grid; grid-template-columns:1fr 1fr; gap:12px; margin-top:12px}
.controls{display:flex; flex-wrap:wrap; gap:8px; align-items:center; margin-top:6px}
.controls.two{grid-template-columns:repeat(2,1fr)}
.controls input, .controls select{
  border:1px solid #e5e7eb; border-radius:10px; padding:8px 10px; min-width:90px; font-size:14px;
}
button{border:none; background:#111; color:#fff; padding:10px 14px; border-radius:12px; cursor:pointer;}
button:hover{opacity:.9}
button:disabled{opacity:.45; cursor:not-allowed}
button.buy{background:var(--green)}
button.sell{background:var(--red)}
button.ghost{background:#fff; color:#111; border:1px solid #e5e7eb}
.footer-actions{margin-top:10px; display:flex; justify-content:center}
.footnote{margin-top:8px; text-align:center; color:var(--muted); font-size:12px}
.hud{display:grid; grid-template-columns:repeat(4,1fr); gap:10px; align-items:center; background:#fff; border-radius:16px; padding:12px; box-shadow:0 6px 20px var(--ring)}
.dice{justify-self:end; color:#334155}

.left .card + .card{margin-top:12px}
.right .card + .card{margin-top:12px}
.player{display:flex; align-items:center; gap:12px}
.avatar.lg{font-size:56px}
.stats{display:grid; grid-template-columns:repeat(2,1fr); gap:6px; width:100%}
.stats div{display:flex; justify-content:space-between; border:1px solid #e5e7eb; border-radius:10px; padding:8px 10px}
.badge{display:inline-block; background:var(--amber); color:#111; border-radius:10px; padding:4px 10px; font-weight:700; margin-top:8px}
.actions{display:flex; gap:8px; margin-top:10px}
.note{margin-top:8px; color:#8a6b00}
.log ul{max-height:200px; overflow:auto; margin:0; padding:0; list-style:none}
.log li{padding:2px 0; border-bottom:1px dashed #eee}
.rules{margin:.2rem 0 0; padding-left:18px}
.sparkline{width:100%; height:160px; background:#fff; border:1px solid #eee; border-radius:12px}
.sparkline.placeholder{height:160px; background:linear-gradient(90deg,#fafafa,#f1f1f1)}
.result{margin-top:12px; padding:12px; border-radius:12px; text-align:center}
.result.win{background:#dcfce7; color:#166534}
.result.lose{background:#fef2f2; color:#991b1b}

/* Animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes flash-green {
  0% { background-color: transparent; }
  50% { background-color: rgba(22, 163, 74, 0.2); }
  100% { background-color: transparent; }
}

@keyframes flash-red {
  0% { background-color: transparent; }
  50% { background-color: rgba(220, 38, 38, 0.2); }
  100% { background-color: transparent; }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

@keyframes confetti {
  0% { transform: translateY(0) rotate(0deg); opacity: 1; }
  100% { transform: translateY(-100px) rotate(720deg); opacity: 0; }
}

.dice.spinning {
  animation: spin 0.5s ease-in-out;
}

.hud.flash-green {
  animation: flash-green 0.6s ease-in-out;
}

.hud.flash-red {
  animation: flash-red 0.6s ease-in-out;
}

.char:hover {
  animation: bounce 0.6s ease-in-out;
}

.confetti {
  position: fixed;
  pointer-events: none;
  z-index: 1000;
  font-size: 20px;
  animation: confetti 2s ease-out forwards;
}

/* Save/Load System */
.save-load {
  margin-top: 12px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.save-load h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: var(--muted);
}

.slot {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  margin: 4px 0;
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.slot-info {
  flex: 1;
}

.slot-details {
  font-size: 12px;
  color: var(--muted);
  margin-top: 2px;
}

.slot-empty {
  font-size: 12px;
  color: var(--muted);
  font-style: italic;
}

.slot-actions {
  display: flex;
  gap: 4px;
}

.slot-actions button {
  padding: 4px 8px;
  font-size: 12px;
  min-width: 50px;
}

/* Event Deck Sidebar */
.event-sidebar {
  width: 280px;
  margin-right: 12px;
}

.event-probabilities, .event-recent {
  margin-top: 12px;
}

.event-probabilities h4, .event-recent h4 {
  margin: 0 0 6px 0;
  font-size: 13px;
  color: var(--muted);
}

.event-prob {
  display: flex;
  justify-content: space-between;
  padding: 4px 0;
  border-bottom: 1px dashed #eee;
  font-size: 13px;
}

.event-name {
  color: var(--ink);
}

.event-chance {
  color: var(--muted);
  font-weight: 600;
}

.recent-event {
  display: flex;
  justify-content: space-between;
  padding: 4px 0;
  border-bottom: 1px dashed #eee;
  font-size: 13px;
}

.event-bias {
  font-weight: 600;
  color: var(--muted);
}

button.small {
  padding: 6px 10px;
  font-size: 12px;
}

.board {
  display: grid;
  grid-template-columns: auto 1.5fr .9fr;
  gap: 12px;
  margin-top: 12px;
}

/* Settings Panel */
.settings {
  margin-top: 12px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.settings h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: var(--muted);
}

.setting {
  margin: 8px 0;
}

.setting label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  cursor: pointer;
}

.setting input[type="checkbox"] {
  margin: 0;
}

.setting input[type="range"] {
  flex: 1;
  margin-left: 8px;
}

@media (max-width: 960px){
  .grid3{grid-template-columns:1fr}
  .row{grid-template-columns:1fr}
  .board{grid-template-columns:1fr !important}
  .event-sidebar{width:100%; margin-right:0; margin-bottom:12px}
}
