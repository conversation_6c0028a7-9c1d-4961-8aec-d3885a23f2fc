“Create a new Vite React TypeScript app named acorn-market. Add the files exactly as below. After you write them, check they compile and run with npm i && npm run dev. No Tailwind. Clean CSS. Responsive UI. Then I’ll ask for polish.”

1) Scaffolding command (local)
npm create vite@latest acorn-market -- --template react-ts
cd acorn-market
npm i

2) Create/replace these files in Cursor
index.html
<!doctype html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Acorn Market</title>
  <link rel="stylesheet" href="/src/styles.css">
</head>
<body>
  <div id="root"></div>
  <script type="module" src="/src/main.tsx"></script>
</body>
</html>

src/main.tsx
import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App";
import "./styles.css";

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);

src/App.tsx
import React, { useEffect, useMemo, useState } from "react";

// -------- types --------
type CharacterKey = "Squeaky" | "Bouncy" | "Tilly";
type Difficulty = "Normal" | "Hard";
type LogItem = { ts: number; text: string };
type EventCard = { key: string; label: string; bias: number; description: string };

// -------- helpers --------
const clamp = (n: number, lo: number, hi: number) => Math.max(lo, Math.min(hi, n));
const round2 = (n: number) => Math.round(n * 100) / 100;
const money = (n: number) => `$${n.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;

// SFX (no assets): tiny beeps via WebAudio
const sfx = {
  click: (freq = 520) => {
    try {
      const ctx = new (window.AudioContext || (window as any).webkitAudioContext)();
      const o = ctx.createOscillator(); const g = ctx.createGain();
      o.connect(g); g.connect(ctx.destination);
      o.type = "sine"; o.frequency.value = freq; g.gain.value = 0.05;
      o.start(); setTimeout(() => { o.stop(); ctx.close(); }, 120);
    } catch {}
  },
  good: () => sfx.click(880),
  bad: () => sfx.click(220),
};

// Dice maps (Normal mirrors your PDF; Hard is spicier)
const DICE_MAPS: Record<Difficulty, Record<number, number>> = {
  Normal: { 1: -0.10, 2: 0, 3: 0, 4: +0.10, 5: +0.10, 6: +0.20 },
  Hard:   { 1: -0.15, 2: -0.05, 3: 0, 4: +0.05, 5: +0.10, 6: +0.15 },
};

// Daily event cards (bias is additive to today’s pct move)
const EVENT_DECK: EventCard[] = [
  { key: "rumor",   label: "Rumor (-5%)",     bias: -0.05, description: "Spooky chatter. Sellers nibble." },
  { key: "harvest", label: "Good Harvest (+5%)", bias: +0.05, description: "Plenty of acorns. Optimism rises." },
  { key: "storm",   label: "Storm (-10%)",    bias: -0.10, description: "Supply chains muddy. Risk-off." },
  { key: "fair",    label: "Market Fair (+10%)", bias: +0.10, description: "Festival boom! Buyers abound." },
];

const STORAGE_KEY = "acorn-market-v1";

export default function App() {
  // ------- state -------
  const [day, setDay]                 = useState(1);
  const [maxDays, setMaxDays]         = useState(20);
  const [price, setPrice]             = useState(1.00);
  const [history, setHistory]         = useState<number[]>([1.00]);
  const [cash, setCash]               = useState(100);
  const [acorns, setAcorns]           = useState(0);
  const [goal, setGoal]               = useState(250);
  const [qty, setQty]                 = useState(1);
  const [selectedChar, setSelectedChar] = useState<CharacterKey | null>(null);
  const [lastRoll, setLastRoll]       = useState<number | null>(null);
  const [lastChange, setLastChange]   = useState(0);
  const [logs, setLogs]               = useState<LogItem[]>([]);
  const [difficulty, setDifficulty]   = useState<Difficulty>("Normal");
  const [eventToday, setEventToday]   = useState<EventCard | null>(null);

  // perks
  const [rerollAvailable, setRerollAvailable] = useState(false);  // Bouncy
  const [rerollUsed, setRerollUsed]           = useState(false);  // Bouncy
  const [squeakyDiscountReady, setSqueakyDiscountReady] = useState(false); // Squeaky (after down move)

  // ------- computed -------
  const netWorth = useMemo(() => round2(cash + acorns * price), [cash, acorns, price]);
  const canBuyQty = useMemo(() => Math.floor(cash / effectivePriceToBuy()), [cash, price, selectedChar, squeakyDiscountReady]);

  const won  = netWorth >= goal;
  const lost = day > maxDays && !won;

  // ------- persistence -------
  useEffect(() => {
    // load
    const raw = localStorage.getItem(STORAGE_KEY);
    if (raw) {
      try {
        const s = JSON.parse(raw);
        setDay(s.day ?? 1); setMaxDays(s.maxDays ?? 20);
        setPrice(s.price ?? 1); setHistory(s.history ?? [1]);
        setCash(s.cash ?? 100); setAcorns(s.acorns ?? 0);
        setGoal(s.goal ?? 250); setQty(s.qty ?? 1);
        setSelectedChar(s.selectedChar ?? null);
        setLastRoll(s.lastRoll ?? null); setLastChange(s.lastChange ?? 0);
        setLogs(s.logs ?? []); setDifficulty(s.difficulty ?? "Normal");
        setEventToday(s.eventToday ?? null);
        setRerollAvailable(s.rerollAvailable ?? false);
        setRerollUsed(s.rerollUsed ?? false);
        setSqueakyDiscountReady(s.squeakyDiscountReady ?? false);
      } catch {}
    } else {
      // seed first day event
      drawEventForToday();
    }
  }, []);

  useEffect(() => {
    // save
    const s = {
      day, maxDays, price, history, cash, acorns, goal, qty,
      selectedChar, lastRoll, lastChange, logs, difficulty,
      eventToday, rerollAvailable, rerollUsed, squeakyDiscountReady
    };
    localStorage.setItem(STORAGE_KEY, JSON.stringify(s));
  }, [day, maxDays, price, history, cash, acorns, goal, qty, selectedChar, lastRoll, lastChange, logs, difficulty, eventToday, rerollAvailable, rerollUsed, squeakyDiscountReady]);

  // ------- utils -------
  function log(msg: string) {
    setLogs(l => [{ ts: Date.now(), text: msg }, ...l].slice(0, 80));
  }

  function resetGame() {
    setDay(1); setMaxDays(20); setPrice(1); setHistory([1]);
    setCash(100); setAcorns(0); setGoal(250); setQty(1);
    setSelectedChar(null); setLastRoll(null); setLastChange(0);
    setLogs([]); setDifficulty("Normal"); setEventToday(null);
    setRerollAvailable(false); setRerollUsed(false); setSqueakyDiscountReady(false);
    drawEventForToday();
  }

  function startAs(c: CharacterKey) {
    setSelectedChar(c);
    setRerollAvailable(c === "Bouncy");
    setRerollUsed(false);
    log(`Started as ${c}.`);
  }

  function effectivePriceToBuy() {
    const discount = selectedChar === "Squeaky" && squeakyDiscountReady ? 0.05 : 0;
    return round2(price * (1 - discount));
  }

  function drawEventForToday() {
    // ~40% chance to draw an event
    const r = Math.random();
    if (r < 0.4) {
      const card = EVENT_DECK[Math.floor(Math.random() * EVENT_DECK.length)];
      setEventToday(card);
      log(`Event: ${card.label} — ${card.description}`);
    } else {
      setEventToday(null);
    }
  }

  function applyRoll(roll: number) {
    const basePct = DICE_MAPS[difficulty][roll as 1|2|3|4|5|6] ?? 0;
    const bias = eventToday?.bias ?? 0;
    // Additive bias; clamp total change to ±50% to avoid blowups
    const pct = clamp(basePct + bias, -0.5, 0.5);
    const newPrice = clamp(round2(price * (1 + pct)), 0.01, 1_000_000);

    setPrice(newPrice);
    setHistory(h => [...h, newPrice].slice(-240));
    setLastRoll(roll);
    setLastChange(pct);

    // Squeaky: if down move, flag discount for next buy (today only)
    if (selectedChar === "Squeaky") setSqueakyDiscountReady(pct < 0);

    const arrow = pct === 0 ? "unchanged" : (pct > 0 ? `up ${pct * 100}%` : `down ${Math.abs(pct) * 100}%`);
    log(`Day ${day}: rolled ${roll}. Price ${arrow} to ${newPrice}.`);
  }

  function rollDice() {
    if (won || lost) return;
    const r = Math.floor(Math.random() * 6) + 1;
    applyRoll(r);
    sfx.click();
  }

  function reroll() {
    if (won || lost) return;
    if (selectedChar !== "Bouncy" || !rerollAvailable || rerollUsed) return;
    const r = Math.floor(Math.random() * 6) + 1;
    applyRoll(r);
    setRerollUsed(true);
    log("Bouncy perk: used reroll.");
    sfx.click(640);
  }

  function buy() {
    const q = Math.max(0, Math.floor(qty));
    if (!q) return;

    const effective = effectivePriceToBuy();
    const cost = round2(q * effective);
    if (cost > cash) { log("Not enough cash."); sfx.bad(); return; }

    setCash(c => round2(c - cost));
    setAcorns(a => a + q);
    log(`Bought ${q} @ ${effective}${(selectedChar === "Squeaky" && squeakyDiscountReady) ? " (Squeaky -5% discount)" : ""}. Cost ${money(cost)}.`);
    // Consume Squeaky discount after 1 buy
    if (selectedChar === "Squeaky" && squeakyDiscountReady) setSqueakyDiscountReady(false);
    sfx.click();
  }

  function sell() {
    const q = Math.max(0, Math.floor(qty));
    if (!q) return;
    if (q > acorns) { log("Not enough acorns."); sfx.bad(); return; }

    const proceeds = round2(q * price);
    setCash(c => round2(c + proceeds));
    setAcorns(a => a - q);
    log(`Sold ${q} @ ${price}. Proceeds ${money(proceeds)}.`);
    sfx.click(700);
  }

  function endDay() {
    if (won || lost) return;

    // Tilly: 1% dividend on holdings
    if (selectedChar === "Tilly" && acorns > 0) {
      const div = round2(acorns * price * 0.01);
      if (div > 0) {
        setCash(c => round2(c + div));
        log(`Tilly dividend: ${money(div)} added to cash.`);
        sfx.good();
      }
    }

    // Reset daily perks
    if (selectedChar === "Bouncy") { setRerollAvailable(true); setRerollUsed(false); }
    setSqueakyDiscountReady(false);

    // Next day
    setDay(d => d + 1);
    drawEventForToday();
  }

  // ------- UI -------
  if (!selectedChar) {
    return (
      <div className="shell">
        <header className="hero">
          <h1>Acorn Market</h1>
          <p>Buy low, sell high, and use your character’s perk to hit the goal before you run out of days.</p>
        </header>

        <section className="card grid3">
          {(["Squeaky","Bouncy","Tilly"] as CharacterKey[]).map((c) => (
            <button key={c} className="char" onClick={() => startAs(c)}>
              <div className="avatar">{c === "Squeaky" ? "🐿️" : c === "Bouncy" ? "🐰" : "🐢"}</div>
              <div className="name">{c}</div>
              <div className="perk">
                {c === "Squeaky" && "5% buy discount after a down move (today only)."}
                {c === "Bouncy" && "One reroll per day."}
                {c === "Tilly"   && "1% dividend on holdings at End Day."}
              </div>
            </button>
          ))}
        </section>

        <section className="row">
          <div className="card">
            <h3>Difficulty</h3>
            <div className="controls">
              <select value={difficulty} onChange={e => setDifficulty(e.target.value as Difficulty)}>
                <option>Normal</option>
                <option>Hard</option>
              </select>
            </div>
            <p className="hint">Normal uses your PDF dice map; Hard adds more downside and less upside.</p>
          </div>
          <div className="card">
            <h3>Goal & Days</h3>
            <div className="controls">
              <label>Goal <input type="number" value={goal} min={50} onChange={e=>setGoal(parseInt(e.target.value||"0",10))}/></label>
              <label>Max Days <input type="number" value={maxDays} min={5} onChange={e=>setMaxDays(parseInt(e.target.value||"0",10))}/></label>
            </div>
          </div>
        </section>

        <div className="footer-actions">
          <button className="ghost" onClick={resetGame}>Reset</button>
        </div>

        <footer className="footnote">Dice rules based on your PDF (1=−10%, 2/3=0%, 4/5=+10%, 6=+20%).</footer>
      </div>
    );
  }

  return (
    <div className="shell">
      <header className="hud">
        <div><b>Day</b> {day} / {maxDays}</div>
        <div><b>Price</b> {price.toFixed(2)}</div>
        <div><b>Net Worth</b> {money(netWorth)}</div>
        <div className="dice">🎲 {lastRoll ?? "—"} {lastChange ? `(${lastChange>0?"+":""}${(lastChange*100).toFixed(0)}%)` : ""}</div>
      </header>

      <section className="board">
        <div className="left">
          <div className="card">
            <h3>Market</h3>
            <Sparkline data={history}/>
            {eventToday ? (
              <div className="badge">{eventToday.label}</div>
            ) : (
              <div className="hint">No special event today.</div>
            )}
            <div className="actions">
              <button onClick={rollDice} disabled={won||lost}>Roll Dice</button>
              <button onClick={reroll} disabled={!(selectedChar==="Bouncy" && rerollAvailable && !rerollUsed) || won || lost}>Use Reroll (Bouncy)</button>
              <button onClick={endDay} disabled={won||lost}>End Day</button>
            </div>
          </div>

          <div className="card">
            <h3>Trade</h3>
            <div className="controls">
              <label>Qty
                <input type="number" min={1} step={1} value={qty} onChange={e=>setQty(parseInt(e.target.value||"0",10))}/>
              </label>
              <button className="buy" onClick={buy}>Buy @ {effectivePriceToBuy().toFixed(2)}</button>
              <button className="sell" onClick={sell}>Sell @ {price.toFixed(2)}</button>
              <div className="hint">You can buy up to <b>{canBuyQty}</b> now.</div>
            </div>
            {(selectedChar==="Squeaky" && squeakyDiscountReady) && <div className="note">Squeaky: 5% discount active (consumed on next buy).</div>}
          </div>

          <div className="card log">
            <h3>Activity</h3>
            <ul>{logs.map(l => <li key={l.ts}>• {l.text}</li>)}</ul>
          </div>
        </div>

        <div className="right">
          <div className="card">
            <h3>Player</h3>
            <div className="player">
              <div className="avatar lg">{selectedChar==="Squeaky"?"🐿️":selectedChar==="Bouncy"?"🐰":"🐢"}</div>
              <div className="stats">
                <div><span>Cash</span><b>{money(cash)}</b></div>
                <div><span>Holdings</span><b>{acorns} acorns</b></div>
                <div><span>Goal</span><b>{money(goal)}</b></div>
                <div><span>Days Left</span><b>{Math.max(0, maxDays - day + 1)}</b></div>
              </div>
            </div>
            <div className="controls two">
              <label>Goal <input type="number" value={goal} min={50} onChange={e=>setGoal(parseInt(e.target.value||"0",10))}/></label>
              <label>Max Days <input type="number" value={maxDays} min={5} onChange={e=>setMaxDays(parseInt(e.target.value||"0",10))}/></label>
              <label>Difficulty
                <select value={difficulty} onChange={e=>setDifficulty(e.target.value as Difficulty)}>
                  <option>Normal</option><option>Hard</option>
                </select>
              </label>
            </div>

            {(won || lost) && (
              <div className={`result ${won ? "win" : "lose"}`}>
                <b>{won ? "Victory!" : "Out of time."}</b>
                <div className="hint">{won ? `You hit ${money(goal)} in ${day} days.` : `Try a different strategy or perk.`}</div>
              </div>
            )}

            <div className="controls">
              <button className="ghost" onClick={resetGame}>New Game</button>
            </div>
          </div>

          <div className="card">
            <h3>Rules</h3>
            <ul className="rules">
              <li>Roll 1 → price −10%</li>
              <li>Roll 2 or 3 → price unchanged</li>
              <li>Roll 4 or 5 → price +10%</li>
              <li>Roll 6 → price +20%</li>
            </ul>
            <p className="hint">Buy low, sell high; use your perk.</p>
          </div>
        </div>
      </section>
    </div>
  );
}

// -------- Sparkline component --------
function Sparkline({ data }: { data: number[] }) {
  const W = 640, H = 160, pad = 10;
  if (data.length < 2) return <div className="sparkline placeholder"/>;
  const min = Math.min(...data), max = Math.max(...data);
  const rng = max - min || 1;
  const pts = data.map((v, i) => {
    const x = pad + (i / (data.length - 1)) * (W - 2 * pad);
    const y = pad + (1 - (v - min) / rng) * (H - 2 * pad);
    return `${x},${y}`;
  }).join(" ");
  const up = data[data.length - 1] >= data[0];
  return (
    <svg className="sparkline" viewBox={`0 0 ${W} ${H}`}>
      <polyline fill="none" stroke={up ? "#16a34a" : "#dc2626"} strokeWidth="3" points={pts} strokeLinejoin="round" strokeLinecap="round"/>
    </svg>
  );
}

src/styles.css
:root{
  --bg:#f6f0e8; --card:#ffffff; --ink:#2b2b2b; --muted:#6b7280;
  --green:#16a34a; --red:#dc2626; --amber:#efb14a; --wood:#a3652a;
  --ring:rgba(0,0,0,.06);
}
*{box-sizing:border-box}
html,body,#root{height:100%}
body{margin:0;background:linear-gradient(180deg,#f9f6f1, #f1eadf); color:var(--ink); font: 16px/1.4 Inter, system-ui, -apple-system, Segoe UI, Roboto, Arial;}
h1,h2,h3{margin:.2rem 0  .6rem}
.shell{max-width:1200px;margin:0 auto;padding:20px}
.hero{text-align:center;margin:16px 0 20px}
.hero h1{font-size:40px;margin-bottom:4px}
.card{background:var(--card); border-radius:16px; padding:16px; box-shadow:0 6px 20px var(--ring)}
.grid3{display:grid; grid-template-columns:repeat(3,1fr); gap:12px}
.char{border:1px solid #e5e7eb; border-radius:16px; background:#fff; padding:16px; text-align:left; cursor:pointer; transition:.2s;}
.char:hover{box-shadow:0 8px 22px var(--ring); transform:translateY(-1px)}
.char .avatar{font-size:44px}
.char .name{font-weight:800; margin:6px 0}
.perk,.hint{color:var(--muted); font-size:13px}
.row{display:grid; grid-template-columns:1fr 1fr; gap:12px; margin-top:12px}
.controls{display:flex; flex-wrap:wrap; gap:8px; align-items:center; margin-top:6px}
.controls.two{grid-template-columns:repeat(2,1fr)}
.controls input, .controls select{
  border:1px solid #e5e7eb; border-radius:10px; padding:8px 10px; min-width:90px; font-size:14px;
}
button{border:none; background:#111; color:#fff; padding:10px 14px; border-radius:12px; cursor:pointer;}
button:hover{opacity:.9}
button:disabled{opacity:.45; cursor:not-allowed}
button.buy{background:var(--green)}
button.sell{background:var(--red)}
button.ghost{background:#fff; color:#111; border:1px solid #e5e7eb}
.footer-actions{margin-top:10px; display:flex; justify-content:center}
.footnote{margin-top:8px; text-align:center; color:var(--muted); font-size:12px}
.hud{display:grid; grid-template-columns:repeat(4,1fr); gap:10px; align-items:center; background:#fff; border-radius:16px; padding:12px; box-shadow:0 6px 20px var(--ring)}
.dice{justify-self:end; color:#334155}
.board{display:grid; grid-template-columns:1.5fr .9fr; gap:12px; margin-top:12px}
.left .card + .card{margin-top:12px}
.right .card + .card{margin-top:12px}
.player{display:flex; align-items:center; gap:12px}
.avatar.lg{font-size:56px}
.stats{display:grid; grid-template-columns:repeat(2,1fr); gap:6px; width:100%}
.stats div{display:flex; justify-content:space-between; border:1px solid #e5e7eb; border-radius:10px; padding:8px 10px}
.badge{display:inline-block; background:var(--amber); color:#111; border-radius:10px; padding:4px 10px; font-weight:700; margin-top:8px}
.actions{display:flex; gap:8px; margin-top:10px}
.note{margin-top:8px; color:#8a6b00}
.log ul{max-height:200px; overflow:auto; margin:0; padding:0; list-style:none}
.log li{padding:2px 0; border-bottom:1px dashed #eee}
.rules{margin:.2rem 0 0; padding-left:18px}
.sparkline{width:100%; height:160px; background:#fff; border:1px solid #eee; border-radius:12px}
.sparkline.placeholder{height:160px; background:linear-gradient(90deg,#fafafa,#f1f1f1)}
@media (max-width: 960px){
  .grid3{grid-template-columns:1fr}
  .row{grid-template-columns:1fr}
  .board{grid-template-columns:1fr}
}

3) Run it
npm run dev


Open the URL it prints. You’ve got the full loop, perks, events, difficulty, autosave, and SFX.

Enhancements you can ask Cursor to add next (copy/paste prompts)

A) “Daily Event Deck v2”
“Add a left sidebar ‘Event Deck’ showing the last 5 events and the odds of each event. Let me toggle events on/off in settings.”

B) “Animations”
“Animate the dice (spin), the price change (green/red flash), and confetti on a 6. Use CSS only, no external libs.”

C) “Character Art Slots”
“Add optional portrait image URLs for each character and render them in the Player card. If missing, fall back to the emoji.”

D) “Save/Load/Share”
“Add Save/Load slots (A/B/C) in localStorage and a ‘Share Run’ button that serializes state into the URL.”

E) “Options Edition Mode”
“Add a second game mode: ‘Options Edition’ (weekly turns, simplified option chain, Covered Calls/CSP/Credit Spreads/Diagonal). Keep the current mode as ‘Classic’. Mode toggle on the title screen.”

F) “Sound Pack”
“Replace WebAudio beeps with three small embedded WAVs (roll, buy/sell, win/lose). Lazy-load; volume slider in Settings.”

G) “Accessibility & Color-blind Safe”
“Add a ‘Reduce Motion’ toggle that disables confetti/leaves; add shapes/arrows with color to indicate up/down moves.”